import { ZodError, ZodIssue } from "zod";
import { IGenericErrorResponse } from "../interface/common";
import { IGenericErrorMessage } from "../interface/error";

const handleZodError = (error: ZodError): IGenericErrorResponse => {
  const errors: IGenericErrorMessage[] = error.issues.map((issue: ZodIssue) => {
    return {
      path: issue?.path[issue.path.length - 1],
      message: issue?.message,
    };
  });
  const statusCode = 400;
  return {
    statusCode,
    message: "validation Error",
    errorMessages: errors,
  };
};

export default handleZodError;
