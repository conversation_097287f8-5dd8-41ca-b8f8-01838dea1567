import { Schema, model } from "mongoose";
import bcrypt from "bcrypt";
import config from "../../../config";
import { IUser, UserModel } from "./auth.interface";
import { roles } from "./auth.constant";

export const UserSchema = new Schema<IUser, UserModel>(
  {
    name: {
      type: {
        firstName: {
          type: String,
          required: true,
          maxlength: 20,
          minlength: 3,
        },
        lastName: {
          type: String,
          required: true,
          maxlength: 20,
          minlength: 3,
        },
      },
      required: true,
    },
    studentId: {
      type: String,
      required: true,
      unique: true,
      maxlength: 11,
      minlength: 11,
    },
    password: {
      type: String,
      required: true,
      select: false,
    },
    role: {
      type: String,
      enum: roles,
      required: true,
      default: "student",
    },
    status: {
      type: String,
      enum: ["active", "leave"],
      default: "active",
    },
    isPinReset: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

UserSchema.statics.isUserExist = async function (
  studentId: string
): Promise<Pick<IUser, "studentId" | "password" | "role"> | null> {
  return await User.findOne({ studentId: studentId }, { studentId: 1, password: 1, role: 1 });
};

UserSchema.statics.isPasswordMatched = async function (
  givenPassword: string,
  savedPassword: string
): Promise<boolean> {
  return await bcrypt.compare(givenPassword, savedPassword);
};

UserSchema.pre("save", async function (next) {
  const user = this;
  user.password = await bcrypt.hash(this.password, Number(config.bcrypt_salt_rounds));
  next();
});

export const User = model<IUser, UserModel>("User", UserSchema);
