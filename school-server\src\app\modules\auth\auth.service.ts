import httpStatus from "http-status";
import ApiError from "../../../errors/ApiError";
import { ILoginUser, ILoginUserResponse } from "./auth.interface";
import { Secret } from "jsonwebtoken";
import config from "../../../config";
import { User } from "../auth/auth.model";
import { jwtHelpers } from "../../../helpers/jwtHelpers";
import { hashingHelper } from "../../../helpers/hashingHelpers";

const loginUser = async (payload: ILoginUser): Promise<ILoginUserResponse> => {
  const { studentId, password } = payload;

  const isUserExist = await User.isUserExist(studentId);
  if (!isUserExist) {
    throw new ApiError(httpStatus.NOT_FOUND, "User does not exist");
  }

  if (
    isUserExist.password &&
    !(await User.isPasswordMatched(password, isUserExist.password))
  ) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Password is incorrect");
  }

  //create access token & refresh token

  const { studentId: userId, role } = isUserExist;
  const accessToken = jwtHelpers.createToken(
    { userId, role },
    config.jwt.secret as Secret,
    config.jwt.expires_in as string
  );

  return {
    accessToken,
  };
};

const changePassword = async (userId: string, oldPassword: string, newPassword: string) => {
  if (newPassword.length !== 6 || isNaN(Number(newPassword))) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "New Password must be a 6-digit"
    );
  }

  const user = await User.findOne({ studentId: userId }).select("+password");

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }
  if (user.password && !(await User.isPasswordMatched(oldPassword, user.password))) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Old Password is incorrect");
  }
  const hashedPassword = await hashingHelper.encrypt_password(newPassword);
  await User.updateOne({ studentId: userId }, { password: hashedPassword });

  return {
    success: true,
    message: "Password changed successfully",
  };
};

export const AuthService = {
  loginUser,
  changePassword,
};
