import { Model } from "mongoose";

export type roleType = "student" | "teacher" | "parent" | "staff" | "admin";
export type statusType = "active" | "leave";

export type ILoginUser = {
  studentId: string;
  password: string;
};

export type FullName = {
  firstName: string;
  lastName: string;
};

export type ILoginUserResponse = {
  accessToken: string;
  refreshToken?: string;
};
export type IRefreshTokenResponse = {
  accessToken: string;
};

export type IUser = {
  name: FullName;
  studentId: string;
  role: string;
  password: string;
  status: statusType;
  isPinReset: boolean;
};

export type UserModel = {
  isUserExist(mobile: string): Promise<Pick<IUser, "studentId" | "password" | "role">>;
  isPasswordMatched(
    givenPassword: string,
    savedPassword: string
  ): Promise<boolean>;
} & Model<IUser>;